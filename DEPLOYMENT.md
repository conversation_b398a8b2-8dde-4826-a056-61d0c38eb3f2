# LayerEdge $Edgen Community - Koyeb Deployment Guide

This guide explains how to deploy the LayerEdge $Edgen Community application to Koyeb.

## Prerequisites

1. **Koyeb Account**: Sign up at [koyeb.com](https://koyeb.com)
2. **GitHub Repository**: Push your code to GitHub
3. **PostgreSQL Database**: Set up a PostgreSQL database (recommended: Neon, Supabase, or Railway)
4. **X (Twitter) API Credentials**: Ensure you have valid Twitter API credentials

## Environment Variables for Koyeb

Set the following environment variables in your Koyeb service configuration:

### Required Variables

```bash
# Database - Supabase PostgreSQL
DATABASE_URL=postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1
DIRECT_URL=postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:5432/postgres

# NextAuth.js
NEXTAUTH_URL=https://your-app-name.koyeb.app
NEXTAUTH_SECRET=layeredge-edgen-community-secret-key-2024

# X (Twitter) OAuth
TWITTER_CLIENT_ID=SzVkU3VsQ0NheWcwMVU1MW8ta1I6MTpjaQ
TWITTER_CLIENT_SECRET=snl_S5q_2RZ1Bk6V7GCyDUoNWuAHxFjnf6Za7W-F2qMz3UUvLS

# X API v2
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAHyCzwEAAAAAh9uE7X3FHoLzdxGTfVwuDVkhDV4%3DcbbsrKkuHDiBFC0PGANM7jD8vrLOd0tnlhr30brsLmXUAxHFTZ

# LayerEdge Community X URL
LAYEREDGE_COMMUNITY_URL=https://x.com/i/communities/1890107751621363

# Node.js
NODE_ENV=production
```

## Deployment Steps

### Option 1: Git-based Deployment (Recommended)

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Prepare for Koyeb deployment"
   git push origin main
   ```

2. **Create Koyeb Service**:
   - Go to [Koyeb Control Panel](https://app.koyeb.com)
   - Click "Create Web Service"
   - Select "GitHub" as deployment option
   - Choose your repository and branch
   - Set the service name (e.g., `layeredge-edgen-community`)

3. **Configure Build Settings**:
   - Build command: `npm run build`
   - Start command: `npm start`
   - Port: `3000`

4. **Set Environment Variables**:
   - Add all the environment variables listed above
   - Make sure `NEXTAUTH_URL` matches your Koyeb app URL

5. **Deploy**:
   - Click "Deploy" to start the deployment process

### Option 2: Docker Deployment

1. **Build and Push Docker Image**:
   ```bash
   docker build -t layeredge-edgen-community .
   docker tag layeredge-edgen-community your-registry/layeredge-edgen-community
   docker push your-registry/layeredge-edgen-community
   ```

2. **Deploy from Container Registry**:
   - In Koyeb, select "Pre-built container"
   - Enter your container image URL
   - Set port to `3000`
   - Add environment variables

## Database Setup

### Using Neon (Recommended)

1. Create a Neon account at [neon.tech](https://neon.tech)
2. Create a new project
3. Copy the connection string
4. Set it as `DATABASE_URL` in Koyeb

### Using Supabase (Current Configuration)

The project is already configured with Supabase PostgreSQL database:

**Database Details:**
- Database identifier: `bzqayhnlogpaxfcmmrlq`
- Region: `aws-0-eu-north-1` (EU North - Stockholm)
- Password: `d234A879a1#`

**Connection Strings:**
- **Transaction Pooler** (for Prisma ORM): `postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1`
- **Session Pooler** (for migrations): `postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:5432/postgres`

**Important Notes:**
- Password is URL-encoded (`#` becomes `%23`)
- Uses IPv4 pooler endpoints for Koyeb compatibility
- Transaction pooler (port 6543) for serverless/ORM operations
- Session pooler (port 5432) for persistent connections and migrations

## Post-Deployment Steps

1. **Run Database Migrations**:
   After deployment, you may need to run:
   ```bash
   npx prisma db push
   ```

2. **Verify X OAuth Callback**:
   - Go to [Twitter Developer Portal](https://developer.twitter.com)
   - Update your app's callback URL to: `https://your-app-name.koyeb.app/api/auth/callback/twitter`

3. **Test the Application**:
   - Visit your deployed app
   - Test the authentication flow
   - Submit a test tweet to verify API integration

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check that all dependencies are properly installed
   - Ensure Prisma generates correctly
   - Verify environment variables are set

2. **Database Connection Issues**:
   - Verify `DATABASE_URL` is correct
   - Ensure database allows external connections
   - Check if database exists and is accessible

3. **Authentication Issues**:
   - Verify Twitter OAuth credentials
   - Check `NEXTAUTH_URL` matches your domain
   - Ensure `NEXTAUTH_SECRET` is set

4. **API Errors**:
   - Verify Twitter Bearer Token is valid
   - Check API rate limits
   - Ensure community URL is correct

### Logs and Monitoring

- Use Koyeb's built-in logging to debug issues
- Monitor application metrics in the Koyeb dashboard
- Set up alerts for critical errors

## Performance Optimization

1. **Database Optimization**:
   - Use connection pooling
   - Add database indexes for frequently queried fields
   - Consider read replicas for high traffic

2. **Caching**:
   - Implement Redis for session storage
   - Cache API responses where appropriate
   - Use CDN for static assets

3. **Monitoring**:
   - Set up application monitoring (e.g., Sentry)
   - Monitor database performance
   - Track API usage and rate limits

## Security Considerations

1. **Environment Variables**:
   - Never commit secrets to version control
   - Use Koyeb's secret management
   - Rotate API keys regularly

2. **Database Security**:
   - Use SSL connections
   - Implement proper access controls
   - Regular security updates

3. **API Security**:
   - Implement rate limiting
   - Validate all inputs
   - Use HTTPS everywhere

## Support

For deployment issues:
- Check Koyeb documentation: [docs.koyeb.com](https://docs.koyeb.com)
- Contact Koyeb support
- Review application logs for specific errors
